# 🐛 WorkMates 调试指南

## 📋 调试配置说明

我已经为你的 Next.js 项目配置了完整的全栈调试环境，现在你可以在任何地方打断点进行调试。

## 🚀 可用的调试配置

### 1. 🚀 Next.js: Full Stack Debug
- **用途**: 启动 Next.js 开发服务器并启用调试
- **特点**: 支持服务端代码调试，自动启动浏览器
- **端口**: 9229 (调试端口)

### 2. 🌐 Next.js: Client-side Debug  
- **用途**: 调试客户端 React 代码
- **特点**: 在 Chrome 中调试前端代码
- **要求**: 需要先启动开发服务器

### 3. 🔧 Next.js: Attach to Server
- **用途**: 附加到已运行的调试服务器
- **特点**: 当服务器已经在调试模式运行时使用

### 4. 🎯 Full Stack Debug (Server + Client)
- **用途**: 同时启动服务端和客户端调试
- **特点**: 一键启动完整调试环境

## 🔧 使用步骤

### 方法一：一键全栈调试（推荐）
1. 按 `F5` 或点击调试面板的运行按钮
2. 选择 "🎯 Full Stack Debug (Server + Client)"
3. 等待服务器启动完成
4. 在任意代码位置打断点
5. 访问 http://localhost:3000 触发断点

### 方法二：分步调试
1. 先选择 "🚀 Next.js: Full Stack Debug" 启动服务器
2. 等待服务器启动完成
3. 再选择 "🌐 Next.js: Client-side Debug" 启动浏览器调试

## 🎯 针对你的问题

你遇到的 `company.averageRating.toFixed(1)` 异常，现在可以这样调试：

1. 在 `src/app/(main)/companies/page.tsx` 第 416 行打断点
2. 启动 "🎯 Full Stack Debug (Server + Client)"
3. 访问公司列表页面
4. 当断点触发时，检查 `company.averageRating` 的值
5. 确认是否为 `null`、`undefined` 或其他非数字类型

## 🛠️ 调试技巧

### 检查变量值
- 鼠标悬停在变量上查看值
- 使用调试控制台执行表达式
- 查看调用堆栈了解代码执行路径

### 条件断点
- 右键断点设置条件，如 `company.averageRating === null`
- 只在特定条件下暂停执行

### 日志断点
- 设置日志断点输出变量值而不暂停执行
- 适合快速检查数据流

## 🔍 常见问题排查

### 如果断点不生效
1. 确保使用了正确的调试配置
2. 检查 source maps 是否正确生成
3. 清理缓存：删除 `.next` 文件夹后重启

### 如果无法连接调试器
1. 检查端口 9229 是否被占用
2. 重启 VSCode 和开发服务器
3. 确保 Node.js 版本兼容（>=18.17.0）

## 📝 环境要求

- ✅ Node.js >= 18.17.0
- ✅ VSCode 调试扩展
- ✅ Chrome 浏览器（用于客户端调试）

## 🎉 开始调试

现在你可以开始调试了！建议先测试一下配置是否正常工作：

1. 在任意 React 组件中打个断点
2. 启动 "🎯 Full Stack Debug (Server + Client)"
3. 访问对应页面验证断点是否触发

祝调试顺利！🚀
