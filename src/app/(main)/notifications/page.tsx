'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Bell, 
  Settings, 
  CheckCheck, 
  Archive, 
  Trash2,
  Filter,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'
import { NotificationItem } from '@/components/notifications/notification-item'
import { 
  getNotifications, 
  markNotificationsAsRead,
  getNotificationStats,
  NotificationItem as NotificationData,
  NotificationStats,
  NotificationStatus
} from '@/lib/notifications'
import { useToast } from '@/hooks/use-toast'
import { useSession } from 'next-auth/react'

export default function NotificationsPage() {
  const { data: session } = useSession()
  const { toast } = useToast()
  
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [stats, setStats] = useState<NotificationStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<NotificationStatus | 'all'>('all')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // 获取通知列表
  const fetchNotifications = async (status?: NotificationStatus, pageNum = 1, append = false) => {
    try {
      setLoading(true)
      const response = await getNotifications({
        status,
        page: pageNum,
        limit: 20,
      })
      
      if (response.success) {
        const newNotifications = response.data.notifications
        if (append) {
          setNotifications(prev => [...prev, ...newNotifications])
        } else {
          setNotifications(newNotifications)
        }
        
        setHasMore(newNotifications.length === 20)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('获取通知失败:', error)
      toast({
        title: '获取通知失败',
        description: '请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 获取通知统计
  const fetchStats = async () => {
    try {
      const response = await getNotificationStats()
      if (response.success) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('获取通知统计失败:', error)
    }
  }

  // 初始加载
  useEffect(() => {
    if (session?.user) {
      fetchNotifications(activeTab === 'all' ? undefined : activeTab)
      fetchStats()
    }
  }, [session?.user, activeTab])

  // 标记所有通知为已读
  const handleMarkAllAsRead = async () => {
    try {
      await markNotificationsAsRead()
      await fetchNotifications(activeTab === 'all' ? undefined : activeTab)
      await fetchStats()
      
      toast({
        title: '操作成功',
        description: '所有通知已标记为已读',
      })
    } catch (error) {
      console.error('标记通知失败:', error)
      toast({
        title: '操作失败',
        description: '请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 刷新通知
  const handleRefresh = () => {
    fetchNotifications(activeTab === 'all' ? undefined : activeTab)
    fetchStats()
  }

  // 加载更多
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      fetchNotifications(activeTab === 'all' ? undefined : activeTab, page + 1, true)
    }
  }

  // 处理通知项更新
  const handleNotificationUpdate = () => {
    fetchNotifications(activeTab === 'all' ? undefined : activeTab)
    fetchStats()
  }

  if (!session?.user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Bell className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">请先登录</h3>
            <p className="text-muted-foreground mb-4">登录后查看您的通知</p>
            <Button asChild>
              <Link href="/auth/login">立即登录</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Bell className="h-6 w-6" />
            <h1 className="text-2xl font-bold">通知中心</h1>
            {stats && stats.overview.unread > 0 && (
              <Badge variant="secondary">
                {stats.overview.unread} 条未读
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            
            {stats && stats.overview.unread > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAllAsRead}
              >
                <CheckCheck className="h-4 w-4 mr-1" />
                全部已读
              </Button>
            )}
            
            <Button variant="outline" size="sm" asChild>
              <Link href="/notifications/settings">
                <Settings className="h-4 w-4 mr-1" />
                设置
              </Link>
            </Button>
          </div>
        </div>

        {/* 统计概览 */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.overview.total}</div>
                <div className="text-sm text-muted-foreground">总通知</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.overview.unread}</div>
                <div className="text-sm text-muted-foreground">未读</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.overview.read}</div>
                <div className="text-sm text-muted-foreground">已读</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-600">{stats.overview.readRate}%</div>
                <div className="text-sm text-muted-foreground">阅读率</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 通知列表 */}
        <Card>
          <CardHeader>
            <CardTitle>通知列表</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
              <div className="px-6 pt-2">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="all">
                    全部 ({stats?.overview.total || 0})
                  </TabsTrigger>
                  <TabsTrigger value="UNREAD">
                    未读 ({stats?.overview.unread || 0})
                  </TabsTrigger>
                  <TabsTrigger value="read">
                    已读 ({stats?.overview.read || 0})
                  </TabsTrigger>
                  <TabsTrigger value="ARCHIVED">
                    归档 ({stats?.overview.archived || 0})
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value={activeTab} className="mt-0">
                {loading && notifications.length === 0 ? (
                  // 加载状态
                  <div className="p-6 space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="flex items-start gap-4 p-4">
                          <div className="w-10 h-10 bg-gray-200 rounded-full" />
                          <div className="flex-1 space-y-2">
                            <div className="h-4 bg-gray-200 rounded w-3/4" />
                            <div className="h-3 bg-gray-200 rounded w-1/2" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : notifications.length > 0 ? (
                  // 通知列表
                  <div>
                    <div className="divide-y">
                      {notifications.map((notification) => (
                        <NotificationItem
                          key={notification.id}
                          notification={notification}
                          onUpdate={handleNotificationUpdate}
                        />
                      ))}
                    </div>
                    
                    {/* 加载更多按钮 */}
                    {hasMore && (
                      <div className="p-6 text-center border-t">
                        <Button
                          variant="outline"
                          onClick={handleLoadMore}
                          disabled={loading}
                        >
                          {loading ? '加载中...' : '加载更多'}
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  // 空状态
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                    <h4 className="font-medium mb-2">暂无通知</h4>
                    <p className="text-sm text-muted-foreground">
                      {activeTab === 'UNREAD' 
                        ? '所有通知都已阅读' 
                        : activeTab === 'ARCHIVED'
                        ? '暂无归档通知'
                        : '暂时没有新通知'}
                    </p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
