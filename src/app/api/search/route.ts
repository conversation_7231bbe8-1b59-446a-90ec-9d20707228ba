import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 全局搜索API
 * GET /api/search
 *
 * 查询参数:
 * - q: 搜索关键词（必需）
 * - types: 搜索类型筛选（companies,posts,users，逗号分隔）
 * - category: 分类筛选（仅对帖子有效）
 * - industry: 行业筛选（仅对企业有效）
 * - sort: 排序方式 (relevance, latest, popular)
 * - limit: 每种类型的结果数量限制
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const query = url.searchParams.get('q')
    const typesParam = url.searchParams.get('types')
    const category = url.searchParams.get('category')
    const industry = url.searchParams.get('industry')
    const sort = url.searchParams.get('sort') || 'relevance'
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10'), 20)

    // 验证必需参数
    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '搜索关键词不能为空',
          error: {
            code: 'MISSING_QUERY',
            message: '请输入搜索关键词',
          },
        },
        { status: 400 }
      )
    }

    if (query.trim().length < 2) {
      return NextResponse.json(
        {
          success: false,
          message: '搜索关键词至少需要2个字符',
          error: {
            code: 'QUERY_TOO_SHORT',
            message: '请输入至少2个字符的搜索关键词',
          },
        },
        { status: 400 }
      )
    }

    // 解析搜索类型
    const searchTypes = typesParam
      ? typesParam.split(',').map(t => t.trim())
      : ['companies', 'posts', 'users']
    const validTypes = ['companies', 'posts', 'users']
    const enabledTypes = searchTypes.filter(type => validTypes.includes(type))

    if (enabledTypes.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '无效的搜索类型',
          error: {
            code: 'INVALID_SEARCH_TYPES',
            message: '有效的搜索类型: companies, posts, users',
          },
        },
        { status: 400 }
      )
    }

    // 并行执行所有搜索
    const searchPromises: Promise<any>[] = []
    const results: any = {
      companies: { data: [], total: 0 },
      posts: { data: [], total: 0 },
      users: { data: [], total: 0 },
    }

    // 企业搜索
    if (enabledTypes.includes('companies')) {
      searchPromises.push(
        searchCompanies(query, industry, sort, limit).then(result => {
          results.companies = result
        })
      )
    }

    // 帖子搜索
    if (enabledTypes.includes('posts')) {
      searchPromises.push(
        searchPosts(query, category, sort, limit).then(result => {
          results.posts = result
        })
      )
    }

    // 用户搜索
    if (enabledTypes.includes('users')) {
      searchPromises.push(
        searchUsers(query, sort, limit).then(result => {
          results.users = result
        })
      )
    }

    // 等待所有搜索完成
    await Promise.all(searchPromises)

    // 计算总体统计
    const totalResults =
      results.companies.total + results.posts.total + results.users.total

    // 生成混合搜索结果（按相关性混合排序）
    const mixedResults = generateMixedResults(results, sort, query)

    // 生成搜索建议和相关信息
    const suggestions = await generateGlobalSearchSuggestions(query)
    const relatedSearches = generateRelatedSearches(query, results)

    return NextResponse.json({
      success: true,
      message: `找到 ${totalResults} 个搜索结果`,
      data: {
        // 分类结果
        companies: results.companies.data,
        posts: results.posts.data,
        users: results.users.data,
        // 混合结果（用于"全部"标签页）
        mixed: mixedResults,
      },
      meta: {
        query: {
          text: query,
          types: enabledTypes,
          category,
          industry,
          sort,
          limit,
        },
        totals: {
          companies: results.companies.total,
          posts: results.posts.total,
          users: results.users.total,
          total: totalResults,
        },
        performance: {
          hasResults: totalResults > 0,
          resultDistribution: {
            companies:
              Math.round((results.companies.total / totalResults) * 100) || 0,
            posts: Math.round((results.posts.total / totalResults) * 100) || 0,
            users: Math.round((results.users.total / totalResults) * 100) || 0,
          },
        },
        suggestions,
        relatedSearches,
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('全局搜索失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '搜索失败',
        error: {
          code: 'SEARCH_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 搜索企业
 */
async function searchCompanies(
  query: string,
  industry?: string | null,
  sort?: string,
  limit: number = 10
) {
  try {
    const where: any = {
      isActive: true,
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { nameEn: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
      ],
    }

    if (industry) {
      where.industry = industry
    }

    let orderBy: any = []
    switch (sort) {
      case 'latest':
        orderBy = [{ createdAt: 'desc' }]
        break
      case 'popular':
        orderBy = [{ averageRating: 'desc' }, { totalRatings: 'desc' }]
        break
      default:
        orderBy = [
          { averageRating: 'desc' },
          { totalRatings: 'desc' },
          { name: 'asc' },
        ]
    }

    const [companies, total] = await Promise.all([
      prisma.company.findMany({
        where,
        take: limit,
        orderBy,
        select: {
          id: true,
          name: true,
          nameEn: true,
          logo: true,
          description: true,
          industry: true,
          size: true,
          isVerified: true,
          averageRating: true,
          totalRatings: true,
          totalSalaries: true,
          totalReviews: true,
        },
      }),
      prisma.company.count({ where }),
    ])

    const enhancedCompanies = companies.map(company => ({
      ...company,
      type: 'company' as const,
      relevanceScore: calculateRelevanceScore(
        query,
        company.name,
        company.description
      ),
      displayName:
        company.nameEn && company.nameEn !== company.name
          ? `${company.name} (${company.nameEn})`
          : company.name,
      subtitle: `${company.industry || '未知行业'} • ${company.totalRatings || 0}条评价`,
      url: `/companies/${company.id}`,
    }))

    return { data: enhancedCompanies, total }
  } catch (error) {
    console.error('企业搜索失败:', error)
    return { data: [], total: 0 }
  }
}

/**
 * 搜索帖子
 */
async function searchPosts(
  query: string,
  category?: string | null,
  sort?: string,
  limit: number = 10
) {
  try {
    const where: any = {
      isPublished: true,
      isDeleted: false,
      OR: [
        { title: { contains: query, mode: 'insensitive' } },
        { content: { contains: query, mode: 'insensitive' } },
        { excerpt: { contains: query, mode: 'insensitive' } },
      ],
    }

    if (category) {
      where.category = category
    }

    let orderBy: any = []
    switch (sort) {
      case 'latest':
        orderBy = [{ createdAt: 'desc' }]
        break
      case 'popular':
        orderBy = [{ likeCount: 'desc' }, { commentCount: 'desc' }]
        break
      default:
        orderBy = [
          { isPinned: 'desc' },
          { likeCount: 'desc' },
          { commentCount: 'desc' },
          { createdAt: 'desc' },
        ]
    }

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        take: limit,
        orderBy,
        select: {
          id: true,
          title: true,
          excerpt: true,
          type: true,
          category: true,
          tags: true,
          isAnonymous: true,
          isPinned: true,
          likeCount: true,
          commentCount: true,
          viewCount: true,
          createdAt: true,
          authorId: true,
          author: {
            select: {
              name: true,
              username: true,
              avatar: true,
            },
          },
        },
      }),
      prisma.post.count({ where }),
    ])

    const enhancedPosts = posts.map(post => ({
      ...post,
      type: 'post' as const,
      relevanceScore: calculateRelevanceScore(query, post.title, post.excerpt),
      displayName: post.title,
      subtitle: `${getTypeDisplay(post.type)} • ${post.likeCount || 0}赞 • ${post.commentCount || 0}评论`,
      authorDisplay: post.isAnonymous
        ? '匿名用户'
        : post.author?.name || post.author?.username || '未知用户',
      timeAgo: getTimeAgo(post.createdAt),
      url: `/forum/${post.id}`,
    }))

    return { data: enhancedPosts, total }
  } catch (error) {
    console.error('帖子搜索失败:', error)
    return { data: [], total: 0 }
  }
}

/**
 * 搜索用户
 */
async function searchUsers(query: string, sort?: string, limit: number = 10) {
  try {
    const where: any = {
      isActive: true,
      emailVerified: { not: null },
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { username: { contains: query, mode: 'insensitive' } },
        { bio: { contains: query, mode: 'insensitive' } },
      ],
    }

    let orderBy: any = []
    switch (sort) {
      case 'latest':
        orderBy = [{ createdAt: 'desc' }]
        break
      case 'popular':
        orderBy = [{ reputation: 'desc' }, { points: 'desc' }]
        break
      default:
        orderBy = [{ reputation: 'desc' }, { points: 'desc' }, { name: 'asc' }]
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        take: limit,
        orderBy,
        select: {
          id: true,
          name: true,
          username: true,
          avatar: true,
          bio: true,
          isVerified: true,
          reputation: true,
          points: true,
          workExperiences: {
            where: {
              verificationStatus: 'APPROVED',
              isCurrent: true,
            },
            select: {
              companyName: true,
              position: true,
            },
            take: 1,
          },
          _count: {
            select: {
              posts: true,
              comments: true,
            },
          },
        },
      }),
      prisma.user.count({ where }),
    ])

    const enhancedUsers = users.map(user => {
      const currentRole =
        user.workExperiences.length > 0
          ? `${user.workExperiences[0].position} @ ${user.workExperiences[0].companyName}`
          : null

      return {
        ...user,
        type: 'user' as const,
        relevanceScore: calculateRelevanceScore(
          query,
          user.name || '',
          user.bio
        ),
        displayName: user.name || user.username || '匿名用户',
        subtitle:
          currentRole ||
          `声誉: ${user.reputation || 0} • 贡献: ${(user._count.posts || 0) + (user._count.comments || 0)}`,
        url: `/profile/${user.username || user.id}`,
      }
    })

    return { data: enhancedUsers, total }
  } catch (error) {
    console.error('用户搜索失败:', error)
    return { data: [], total: 0 }
  }
}

/**
 * 计算相关性评分
 */
function calculateRelevanceScore(
  query: string,
  title: string,
  content?: string | null
): number {
  const lowerQuery = query.toLowerCase()
  const lowerTitle = title.toLowerCase()
  const lowerContent = content?.toLowerCase() || ''

  let score = 0

  // 标题匹配加分
  if (lowerTitle === lowerQuery) score += 100
  else if (lowerTitle.startsWith(lowerQuery)) score += 80
  else if (lowerTitle.includes(lowerQuery)) score += 60

  // 内容匹配加分
  if (lowerContent.includes(lowerQuery)) score += 20

  return score
}

/**
 * 生成混合搜索结果
 */
function generateMixedResults(results: any, sort: string, query: string) {
  const allResults: any[] = [
    ...results.companies.data,
    ...results.posts.data,
    ...results.users.data,
  ]

  // 按相关性或其他标准排序
  if (sort === 'relevance') {
    allResults.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
  } else if (sort === 'latest') {
    allResults.sort(
      (a, b) =>
        new Date(b.createdAt || 0).getTime() -
        new Date(a.createdAt || 0).getTime()
    )
  }

  // 限制混合结果数量并实现交错显示
  return allResults.slice(0, 20)
}

/**
 * 生成全局搜索建议
 */
async function generateGlobalSearchSuggestions(query: string) {
  try {
    // 搜索历史和热门搜索词可以在这里实现
    // 暂时返回基于当前查询的简单建议
    const suggestions = {
      popular: ['腾讯', '阿里巴巴', '字节跳动', '华为', '小米'],
      related: [],
      trending: ['远程工作', '跳槽', '面试经验', '薪资调研', '职场发展'],
    }

    return suggestions
  } catch (error) {
    console.error('生成搜索建议失败:', error)
    return { popular: [], related: [], trending: [] }
  }
}

/**
 * 生成相关搜索
 */
function generateRelatedSearches(query: string, results: any) {
  const related: string[] = []

  // 基于搜索结果生成相关搜索
  if (results.companies.data.length > 0) {
    const industries = [
      ...new Set(
        results.companies.data.map((c: any) => c.industry).filter(Boolean)
      ),
    ]
    related.push(...industries.slice(0, 3))
  }

  if (results.posts.data.length > 0) {
    const categories = [
      ...new Set(
        results.posts.data.map((p: any) => p.category).filter(Boolean)
      ),
    ]
    related.push(...categories.slice(0, 3))
  }

  return related.slice(0, 5)
}

/**
 * 获取帖子类型显示名称
 */
function getTypeDisplay(type: string | null): string {
  const typeMap: Record<string, string> = {
    DISCUSSION: '讨论',
    QUESTION: '提问',
    SHARING: '分享',
    NEWS: '资讯',
    REVIEW: '评价',
    JOB: '招聘',
  }
  return type ? typeMap[type] || type : '帖子'
}

/**
 * 获取时间距现在的描述
 */
function getTimeAgo(date: Date | null): string {
  if (!date) return '未知时间'

  const now = new Date()
  const diffMs = now.getTime() - new Date(date).getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 30) return `${diffDays}天前`

  return new Date(date).toLocaleDateString('zh-CN')
}
