import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NotificationPriority, NotificationStatus, NotificationType } from '@prisma/client'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 获取用户通知列表
 * GET /api/notifications
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 50)
    const status = searchParams.get('status') as NotificationStatus | null
    const type = searchParams.get('type') as NotificationType | null
    const priority = searchParams.get('priority') as NotificationPriority | null

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {
      userId: session.user.id,
    }

    if (status) {
      where.status = status
    }

    if (type) {
      where.type = type
    }

    if (priority) {
      where.priority = priority
    }

    // 获取通知列表和总数
    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              username: true,
              avatar: true,
            },
          },
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' },
        ],
        skip,
        take: limit,
      }),
      prisma.notification.count({ where }),
    ])

    // 获取未读通知数量
    const unreadCount = await prisma.notification.count({
      where: {
        userId: session.user.id,
        status: 'UNREAD',
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        unreadCount,
      },
    })
  } catch (error) {
    console.error('获取通知列表失败:', error)
    return NextResponse.json(
      { success: false, message: '获取通知列表失败' },
      { status: 500 }
    )
  }
}

/**
 * 创建通知
 * POST /api/notifications
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      userId,
      type,
      title,
      content,
      priority = 'NORMAL',
      relatedId,
      relatedType,
      actionUrl,
      actionText,
      metadata,
      expiresAt,
    } = body

    // 验证必填字段
    if (!userId || !type || !title) {
      return NextResponse.json(
        { success: false, message: '缺少必填字段' },
        { status: 400 }
      )
    }

    // 检查目标用户是否存在
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
    })

    if (!targetUser) {
      return NextResponse.json(
        { success: false, message: '目标用户不存在' },
        { status: 404 }
      )
    }

    // 检查用户通知设置
    const notificationSettings = await prisma.notificationSetting.findUnique({
      where: { userId },
    })

    // 如果用户有通知设置，检查是否允许此类型的通知
    if (notificationSettings && !isNotificationEnabled(type, notificationSettings)) {
      return NextResponse.json({
        success: true,
        message: '用户已关闭此类型通知',
        data: null,
      })
    }

    // 创建通知
    const notification = await prisma.notification.create({
      data: {
        userId,
        type,
        title,
        content,
        priority,
        relatedId,
        relatedType,
        senderId: session.user.id,
        senderName: session.user.name,
        senderAvatar: session.user.image,
        actionUrl,
        actionText,
        metadata,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      message: '通知创建成功',
      data: notification,
    })
  } catch (error) {
    console.error('创建通知失败:', error)
    return NextResponse.json(
      { success: false, message: '创建通知失败' },
      { status: 500 }
    )
  }
}

/**
 * 批量标记通知为已读
 * PATCH /api/notifications
 */
export async function PATCH(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { notificationIds, action } = body

    if (action === 'markAllRead') {
      // 标记所有未读通知为已读
      await prisma.notification.updateMany({
        where: {
          userId: session.user.id,
          status: 'UNREAD',
        },
        data: {
          status: 'READ',
          readAt: new Date(),
        },
      })

      return NextResponse.json({
        success: true,
        message: '所有通知已标记为已读',
      })
    }

    if (action === 'markRead' && notificationIds?.length > 0) {
      // 标记指定通知为已读
      await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: session.user.id,
        },
        data: {
          status: 'READ',
          readAt: new Date(),
        },
      })

      return NextResponse.json({
        success: true,
        message: '通知已标记为已读',
      })
    }

    return NextResponse.json(
      { success: false, message: '无效的操作' },
      { status: 400 }
    )
  } catch (error) {
    console.error('批量操作通知失败:', error)
    return NextResponse.json(
      { success: false, message: '操作失败' },
      { status: 500 }
    )
  }
}

/**
 * 检查通知类型是否启用
 */
function isNotificationEnabled(type: NotificationType, settings: any): boolean {
  const typeSettingMap: Record<NotificationType, string> = {
    LIKE: 'enableLike',
    COMMENT: 'enableComment',
    REPLY: 'enableReply',
    FOLLOW: 'enableFollow',
    MENTION: 'enableMention',
    JOB_INVITATION: 'enableJobInvitation',
    INTERVIEW_INVITE: 'enableInterviewInvite',
    SALARY_REQUEST: 'enableSalaryRequest',
    COMPANY_UPDATE: 'enableCompanyUpdate',
    POST_FEATURED: 'enablePostFeatured',
    POST_APPROVED: 'enablePostApproved',
    POST_REJECTED: 'enablePostRejected',
    CONTENT_REPORTED: 'enableContentReported',
    SYSTEM_UPDATE: 'enableSystemUpdate',
    SECURITY_ALERT: 'enableSecurityAlert',
    ACCOUNT_VERIFIED: 'enableAccountVerified',
    POLICY_UPDATE: 'enablePolicyUpdate',
    MAINTENANCE: 'enableMaintenance',
    PRIVATE_MESSAGE: 'enablePrivateMessage',
    GROUP_MESSAGE: 'enableGroupMessage',
  }

  const settingKey = typeSettingMap[type]
  return settingKey ? settings[settingKey] : true
}
