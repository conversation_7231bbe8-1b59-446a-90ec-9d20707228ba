import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 获取单个通知详情
 * GET /api/notifications/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const notification = await prisma.notification.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
      },
    })

    if (!notification) {
      return NextResponse.json(
        { success: false, message: '通知不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: notification,
    })
  } catch (error) {
    console.error('获取通知详情失败:', error)
    return NextResponse.json(
      { success: false, message: '获取通知详情失败' },
      { status: 500 }
    )
  }
}

/**
 * 更新通知状态
 * PUT /api/notifications/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { status } = body

    // 验证状态值
    if (!['UNREAD', 'READ', 'ARCHIVED'].includes(status)) {
      return NextResponse.json(
        { success: false, message: '无效的状态值' },
        { status: 400 }
      )
    }

    // 检查通知是否存在且属于当前用户
    const existingNotification = await prisma.notification.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    })

    if (!existingNotification) {
      return NextResponse.json(
        { success: false, message: '通知不存在' },
        { status: 404 }
      )
    }

    // 更新通知状态
    const updateData: any = { status }

    if (status === 'READ' && existingNotification.status === 'UNREAD') {
      updateData.readAt = new Date()
    }

    if (status === 'ARCHIVED') {
      updateData.archivedAt = new Date()
    }

    const notification = await prisma.notification.update({
      where: { id: params.id },
      data: updateData,
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            username: true,
            avatar: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      message: '通知状态更新成功',
      data: notification,
    })
  } catch (error) {
    console.error('更新通知状态失败:', error)
    return NextResponse.json(
      { success: false, message: '更新通知状态失败' },
      { status: 500 }
    )
  }
}

/**
 * 删除通知
 * DELETE /api/notifications/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    // 检查通知是否存在且属于当前用户
    const existingNotification = await prisma.notification.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    })

    if (!existingNotification) {
      return NextResponse.json(
        { success: false, message: '通知不存在' },
        { status: 404 }
      )
    }

    // 删除通知
    await prisma.notification.delete({
      where: { id: params.id },
    })

    return NextResponse.json({
      success: true,
      message: '通知删除成功',
    })
  } catch (error) {
    console.error('删除通知失败:', error)
    return NextResponse.json(
      { success: false, message: '删除通知失败' },
      { status: 500 }
    )
  }
}
