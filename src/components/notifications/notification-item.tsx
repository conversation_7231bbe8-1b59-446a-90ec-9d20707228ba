'use client'

import { useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  MoreHorizontal, 
  Check, 
  Archive, 
  Trash2, 
  ExternalLink,
  Heart,
  MessageCircle,
  UserPlus,
  Briefcase,
  Star,
  AlertTriangle,
  Settings,
  Mail
} from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { 
  NotificationItem as NotificationData,
  markNotificationAsRead,
  archiveNotification,
  deleteNotification,
  getNotificationTypeText,
  getNotificationPriorityText
} from '@/lib/notifications'
import { useToast } from '@/hooks/use-toast'

interface NotificationItemProps {
  notification: NotificationData
  onUpdate?: () => void
  showActions?: boolean
}

export function NotificationItem({ 
  notification, 
  onUpdate,
  showActions = true 
}: NotificationItemProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)

  // 获取通知类型图标
  const getNotificationIcon = () => {
    const iconMap = {
      LIKE: Heart,
      COMMENT: MessageCircle,
      REPLY: MessageCircle,
      FOLLOW: UserPlus,
      MENTION: MessageCircle,
      JOB_INVITATION: Briefcase,
      INTERVIEW_INVITE: Briefcase,
      SALARY_REQUEST: Star,
      COMPANY_UPDATE: Briefcase,
      POST_FEATURED: Star,
      POST_APPROVED: Check,
      POST_REJECTED: AlertTriangle,
      CONTENT_REPORTED: AlertTriangle,
      SYSTEM_UPDATE: Settings,
      SECURITY_ALERT: AlertTriangle,
      ACCOUNT_VERIFIED: Check,
      POLICY_UPDATE: Settings,
      MAINTENANCE: Settings,
      PRIVATE_MESSAGE: Mail,
      GROUP_MESSAGE: Mail,
    }
    
    const IconComponent = iconMap[notification.type] || Settings
    return <IconComponent className="h-4 w-4" />
  }

  // 获取优先级颜色
  const getPriorityColor = () => {
    switch (notification.priority) {
      case 'URGENT':
        return 'text-red-500'
      case 'HIGH':
        return 'text-orange-500'
      case 'NORMAL':
        return 'text-blue-500'
      case 'LOW':
        return 'text-gray-500'
      default:
        return 'text-blue-500'
    }
  }

  // 标记为已读
  const handleMarkAsRead = async () => {
    if (notification.status === 'READ') return

    try {
      setLoading(true)
      await markNotificationAsRead(notification.id)
      onUpdate?.()
      
      toast({
        title: '操作成功',
        description: '通知已标记为已读',
      })
    } catch (error) {
      console.error('标记通知失败:', error)
      toast({
        title: '操作失败',
        description: '请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 归档通知
  const handleArchive = async () => {
    try {
      setLoading(true)
      await archiveNotification(notification.id)
      onUpdate?.()
      
      toast({
        title: '操作成功',
        description: '通知已归档',
      })
    } catch (error) {
      console.error('归档通知失败:', error)
      toast({
        title: '操作失败',
        description: '请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 删除通知
  const handleDelete = async () => {
    try {
      setLoading(true)
      await deleteNotification(notification.id)
      onUpdate?.()
      
      toast({
        title: '操作成功',
        description: '通知已删除',
      })
    } catch (error) {
      console.error('删除通知失败:', error)
      toast({
        title: '操作失败',
        description: '请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理点击通知
  const handleNotificationClick = () => {
    // 如果未读，先标记为已读
    if (notification.status === 'UNREAD') {
      handleMarkAsRead()
    }

    // 如果有操作链接，跳转
    if (notification.actionUrl) {
      window.open(notification.actionUrl, '_blank')
    }
  }

  const timeAgo = formatDistanceToNow(new Date(notification.createdAt), {
    addSuffix: true,
    locale: zhCN,
  })

  return (
    <div 
      className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
        notification.status === 'UNREAD' ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
      }`}
      onClick={handleNotificationClick}
    >
      <div className="flex items-start gap-3">
        {/* 发送者头像或图标 */}
        <div className="flex-shrink-0">
          {notification.sender?.avatar ? (
            <Avatar className="h-8 w-8">
              <AvatarImage src={notification.sender.avatar} />
              <AvatarFallback>
                {notification.sender.name?.[0] || notification.senderName?.[0] || 'S'}
              </AvatarFallback>
            </Avatar>
          ) : (
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getPriorityColor()} bg-current/10`}>
              {getNotificationIcon()}
            </div>
          )}
        </div>

        {/* 通知内容 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="outline" className="text-xs">
                  {getNotificationTypeText(notification.type)}
                </Badge>
                {notification.priority !== 'NORMAL' && (
                  <Badge 
                    variant={notification.priority === 'URGENT' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {getNotificationPriorityText(notification.priority)}
                  </Badge>
                )}
                {notification.status === 'UNREAD' && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                )}
              </div>
              
              <h4 className="font-medium text-sm mb-1 line-clamp-2">
                {notification.title}
              </h4>
              
              {notification.content && (
                <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                  {notification.content}
                </p>
              )}
              
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{timeAgo}</span>
                {notification.sender?.name && (
                  <>
                    <span>•</span>
                    <span>{notification.sender.name}</span>
                  </>
                )}
              </div>
            </div>

            {/* 操作菜单 */}
            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0"
                    disabled={loading}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {notification.status === 'UNREAD' && (
                    <DropdownMenuItem onClick={handleMarkAsRead}>
                      <Check className="h-4 w-4 mr-2" />
                      标记为已读
                    </DropdownMenuItem>
                  )}
                  
                  {notification.actionUrl && (
                    <DropdownMenuItem asChild>
                      <Link href={notification.actionUrl} target="_blank">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        查看详情
                      </Link>
                    </DropdownMenuItem>
                  )}
                  
                  {notification.status !== 'ARCHIVED' && (
                    <DropdownMenuItem onClick={handleArchive}>
                      <Archive className="h-4 w-4 mr-2" />
                      归档
                    </DropdownMenuItem>
                  )}
                  
                  <DropdownMenuItem 
                    onClick={handleDelete}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
