'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon, X, Filter, RotateCcw } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useState } from 'react'

export interface AdvancedSearchFilters {
  // 通用筛选
  query: string
  type: string
  sort: string
  dateFrom?: Date
  dateTo?: Date
  
  // 企业筛选
  industry?: string
  companySize?: string
  location?: string
  verified?: boolean
  minRating?: number
  hasReviews?: boolean
  hasSalaries?: boolean
  
  // 帖子筛选
  postType?: string
  category?: string
  tags?: string[]
  companyId?: string
  anonymous?: boolean
  hasComments?: boolean
  minLikes?: number
  
  // 用户筛选
  userType?: string
  hasAvatar?: boolean
}

interface AdvancedSearchProps {
  filters: AdvancedSearchFilters
  onFiltersChange: (filters: AdvancedSearchFilters) => void
  onSearch: () => void
  onReset: () => void
}

const industries = [
  '互联网',
  '金融',
  '教育',
  '医疗',
  '制造业',
  '房地产',
  '零售',
  '咨询',
  '媒体',
  '游戏',
  '电商',
  '人工智能',
  '区块链',
  '新能源',
  '生物科技'
]

const companySizes = [
  '1-50人',
  '51-200人',
  '201-500人',
  '501-1000人',
  '1000-5000人',
  '5000人以上'
]

const locations = [
  '北京',
  '上海',
  '深圳',
  '广州',
  '杭州',
  '成都',
  '武汉',
  '西安',
  '南京',
  '苏州',
  '天津',
  '重庆'
]

const postTypes = [
  { value: 'DISCUSSION', label: '讨论' },
  { value: 'QUESTION', label: '问答' },
  { value: 'SHARING', label: '分享' },
  { value: 'NEWS', label: '资讯' },
  { value: 'REVIEW', label: '评价' },
  { value: 'JOB', label: '招聘' }
]

const categories = [
  '技术',
  '产品',
  '设计',
  '运营',
  '市场',
  '销售',
  '人事',
  '财务',
  '法务',
  '其他'
]

const commonTags = [
  '面试',
  '薪资',
  '跳槽',
  '职业规划',
  '工作环境',
  '团队管理',
  '技术分享',
  '行业分析',
  '创业',
  '远程工作'
]

export function AdvancedSearch({ filters, onFiltersChange, onSearch, onReset }: AdvancedSearchProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const updateFilter = (key: keyof AdvancedSearchFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const addTag = (tag: string) => {
    const currentTags = filters.tags || []
    if (!currentTags.includes(tag)) {
      updateFilter('tags', [...currentTags, tag])
    }
  }

  const removeTag = (tag: string) => {
    const currentTags = filters.tags || []
    updateFilter('tags', currentTags.filter(t => t !== tag))
  }

  const hasActiveFilters = () => {
    return filters.industry || 
           filters.companySize || 
           filters.location || 
           filters.verified || 
           filters.minRating || 
           filters.hasReviews || 
           filters.hasSalaries || 
           filters.postType || 
           filters.category || 
           (filters.tags && filters.tags.length > 0) || 
           filters.anonymous || 
           filters.hasComments || 
           filters.minLikes || 
           filters.dateFrom || 
           filters.dateTo
  }

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            高级搜索
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters() && (
              <Button variant="outline" size="sm" onClick={onReset}>
                <RotateCcw className="h-4 w-4 mr-1" />
                重置
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? '收起' : '展开'}
            </Button>
          </div>
        </div>
      </CardHeader>

      {showAdvanced && (
        <CardContent className="space-y-6">
          {/* 基础筛选 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="type">搜索类型</Label>
              <Select value={filters.type} onValueChange={(value) => updateFilter('type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="companies">企业</SelectItem>
                  <SelectItem value="posts">帖子</SelectItem>
                  <SelectItem value="users">用户</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="sort">排序方式</Label>
              <Select value={filters.sort} onValueChange={(value) => updateFilter('sort', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择排序" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">相关性</SelectItem>
                  <SelectItem value="latest">最新发布</SelectItem>
                  <SelectItem value="popular">最受欢迎</SelectItem>
                  <SelectItem value="hot">最热门</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>时间范围</Label>
              <div className="flex gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm" className="flex-1">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      {filters.dateFrom ? format(filters.dateFrom, 'MM/dd', { locale: zhCN }) : '开始'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.dateFrom}
                      onSelect={(date) => updateFilter('dateFrom', date)}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm" className="flex-1">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      {filters.dateTo ? format(filters.dateTo, 'MM/dd', { locale: zhCN }) : '结束'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.dateTo}
                      onSelect={(date) => updateFilter('dateTo', date)}
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          <Separator />

          {/* 企业筛选 */}
          {(filters.type === 'all' || filters.type === 'companies') && (
            <div className="space-y-4">
              <h4 className="font-medium">企业筛选</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>行业</Label>
                  <Select value={filters.industry} onValueChange={(value) => updateFilter('industry', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择行业" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部行业</SelectItem>
                      {industries.map(industry => (
                        <SelectItem key={industry} value={industry}>{industry}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>公司规模</Label>
                  <Select value={filters.companySize} onValueChange={(value) => updateFilter('companySize', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择规模" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部规模</SelectItem>
                      {companySizes.map(size => (
                        <SelectItem key={size} value={size}>{size}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>地区</Label>
                  <Select value={filters.location} onValueChange={(value) => updateFilter('location', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择地区" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部地区</SelectItem>
                      {locations.map(location => (
                        <SelectItem key={location} value={location}>{location}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>最低评分</Label>
                  <Select 
                    value={filters.minRating?.toString()} 
                    onValueChange={(value) => updateFilter('minRating', value ? parseFloat(value) : undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择评分" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">不限</SelectItem>
                      <SelectItem value="4.5">4.5分以上</SelectItem>
                      <SelectItem value="4.0">4.0分以上</SelectItem>
                      <SelectItem value="3.5">3.5分以上</SelectItem>
                      <SelectItem value="3.0">3.0分以上</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-4 pt-6">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="verified"
                      checked={filters.verified || false}
                      onCheckedChange={(checked) => updateFilter('verified', checked)}
                    />
                    <Label htmlFor="verified">已认证企业</Label>
                  </div>
                </div>

                <div className="flex items-center space-x-4 pt-6">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasReviews"
                      checked={filters.hasReviews || false}
                      onCheckedChange={(checked) => updateFilter('hasReviews', checked)}
                    />
                    <Label htmlFor="hasReviews">有评价</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasSalaries"
                      checked={filters.hasSalaries || false}
                      onCheckedChange={(checked) => updateFilter('hasSalaries', checked)}
                    />
                    <Label htmlFor="hasSalaries">有薪资</Label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 帖子筛选 */}
          {(filters.type === 'all' || filters.type === 'posts') && (
            <>
              <Separator />
              <div className="space-y-4">
                <h4 className="font-medium">帖子筛选</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label>帖子类型</Label>
                    <Select value={filters.postType} onValueChange={(value) => updateFilter('postType', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">全部类型</SelectItem>
                        {postTypes.map(type => (
                          <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>分类</Label>
                    <Select value={filters.category} onValueChange={(value) => updateFilter('category', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">全部分类</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category} value={category}>{category}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>最少点赞数</Label>
                    <Input
                      type="number"
                      placeholder="输入数字"
                      value={filters.minLikes || ''}
                      onChange={(e) => updateFilter('minLikes', e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                  </div>
                </div>

                <div>
                  <Label>标签</Label>
                  <div className="flex flex-wrap gap-2 mt-2 mb-2">
                    {commonTags.map(tag => (
                      <Button
                        key={tag}
                        variant={filters.tags?.includes(tag) ? "default" : "outline"}
                        size="sm"
                        onClick={() => filters.tags?.includes(tag) ? removeTag(tag) : addTag(tag)}
                      >
                        {tag}
                      </Button>
                    ))}
                  </div>
                  {filters.tags && filters.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {filters.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="anonymous"
                      checked={filters.anonymous || false}
                      onCheckedChange={(checked) => updateFilter('anonymous', checked)}
                    />
                    <Label htmlFor="anonymous">仅匿名帖子</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasComments"
                      checked={filters.hasComments || false}
                      onCheckedChange={(checked) => updateFilter('hasComments', checked)}
                    />
                    <Label htmlFor="hasComments">有评论</Label>
                  </div>
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* 操作按钮 */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onReset}>
              重置筛选
            </Button>
            <Button onClick={onSearch}>
              应用筛选
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
