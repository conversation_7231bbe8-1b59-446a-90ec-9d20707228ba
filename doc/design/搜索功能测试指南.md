# 搜索功能测试指南

## 📋 功能概述

WorkMates 搜索系统提供全面的搜索功能，支持企业、帖子、用户的全局搜索，以及高级筛选功能。

## 🎯 测试目标

验证搜索功能的完整性、准确性和用户体验，确保所有搜索场景都能正常工作。

## 🔧 功能特性

### 1. 全局搜索
- **基础搜索**: 支持关键词搜索企业、帖子、用户
- **分类搜索**: 按类型筛选搜索结果
- **排序功能**: 支持相关性、时间、热度等排序
- **实时搜索**: 输入关键词即时显示结果

### 2. 高级搜索
- **企业筛选**: 行业、规模、地区、评分等
- **帖子筛选**: 类型、分类、标签、时间等
- **用户筛选**: 用户类型、认证状态等
- **时间范围**: 支持自定义时间范围筛选

### 3. 搜索结果展示
- **多标签页**: 全部、企业、帖子、用户分类展示
- **结果统计**: 显示各类型结果数量
- **详细信息**: 显示标题、描述、标签、评分等
- **快速跳转**: 点击结果直接跳转到详情页

## 🧪 测试用例

### 基础搜索测试

#### TC001: 空搜索状态
**测试步骤:**
1. 访问 `/search` 页面
2. 不输入任何关键词

**预期结果:**
- 显示"开始搜索"的空状态页面
- 提示用户输入关键词
- 搜索按钮处于可用状态

#### TC002: 关键词搜索
**测试步骤:**
1. 在搜索框输入 "腾讯"
2. 点击搜索按钮或按回车

**预期结果:**
- 显示包含"腾讯"的搜索结果
- 结果按相关性排序
- 显示结果统计信息
- URL 更新为 `/search?q=腾讯`

#### TC003: 无结果搜索
**测试步骤:**
1. 输入不存在的关键词 "xyz123abc"
2. 执行搜索

**预期结果:**
- 显示"未找到相关结果"页面
- 提供搜索建议
- 显示"清除搜索"按钮

### 分类搜索测试

#### TC004: 企业搜索
**测试步骤:**
1. 搜索 "科技"
2. 切换到"企业"标签页

**预期结果:**
- 只显示企业类型的结果
- 显示企业相关信息（地址、评分、行业等）
- 结果数量统计正确

#### TC005: 帖子搜索
**测试步骤:**
1. 搜索 "面试"
2. 切换到"帖子"标签页

**预期结果:**
- 只显示帖子类型的结果
- 显示帖子信息（作者、时间、点赞数等）
- 支持匿名帖子显示

#### TC006: 用户搜索
**测试步骤:**
1. 搜索用户名
2. 切换到"用户"标签页

**预期结果:**
- 只显示用户类型的结果
- 显示用户基本信息
- 保护用户隐私信息

### 高级搜索测试

#### TC007: 企业高级筛选
**测试步骤:**
1. 展开高级搜索
2. 设置筛选条件：
   - 行业：互联网
   - 规模：1000人以上
   - 地区：北京
   - 最低评分：4.0
3. 应用筛选

**预期结果:**
- 结果符合所有筛选条件
- 筛选条件在界面上正确显示
- 可以清除单个或全部筛选条件

#### TC008: 帖子高级筛选
**测试步骤:**
1. 设置帖子筛选：
   - 类型：讨论
   - 分类：技术
   - 标签：面试、跳槽
   - 最少点赞：10
2. 应用筛选

**预期结果:**
- 结果匹配筛选条件
- 标签筛选正确工作
- 数值筛选准确

#### TC009: 时间范围筛选
**测试步骤:**
1. 设置时间范围筛选
2. 选择最近一个月
3. 应用筛选

**预期结果:**
- 只显示指定时间范围内的结果
- 时间显示正确
- 日期选择器工作正常

### 排序功能测试

#### TC010: 相关性排序
**测试步骤:**
1. 搜索关键词
2. 选择"相关性"排序

**预期结果:**
- 结果按相关性得分排序
- 最相关的结果排在前面

#### TC011: 时间排序
**测试步骤:**
1. 搜索关键词
2. 选择"最新发布"排序

**预期结果:**
- 结果按发布时间倒序排列
- 最新的内容排在前面

#### TC012: 热度排序
**测试步骤:**
1. 搜索关键词
2. 选择"最热门"排序

**预期结果:**
- 结果按热度（浏览量、点赞数等）排序
- 最热门的内容排在前面

### 用户体验测试

#### TC013: 响应式设计
**测试步骤:**
1. 在不同设备上访问搜索页面
2. 测试移动端和桌面端

**预期结果:**
- 在所有设备上正常显示
- 移动端操作流畅
- 高级搜索在移动端可用

#### TC014: 加载状态
**测试步骤:**
1. 执行搜索操作
2. 观察加载状态

**预期结果:**
- 显示加载动画
- 搜索按钮显示"搜索中..."
- 加载完成后正确显示结果

#### TC015: 错误处理
**测试步骤:**
1. 模拟网络错误
2. 执行搜索

**预期结果:**
- 显示友好的错误提示
- 提供重试选项
- 不会导致页面崩溃

## 🔍 API 测试

### 全局搜索 API
```bash
# 基础搜索
GET /api/search?q=腾讯

# 分类搜索
GET /api/search?q=腾讯&types=companies

# 排序搜索
GET /api/search?q=腾讯&sort=latest
```

### 企业搜索 API
```bash
# 企业筛选
GET /api/search/companies?q=科技&industry=互联网&location=北京

# 评分筛选
GET /api/search/companies?q=科技&minRating=4.0&verified=true
```

### 帖子搜索 API
```bash
# 帖子类型筛选
GET /api/search/posts?q=面试&type=DISCUSSION&category=技术

# 标签筛选
GET /api/search/posts?q=面试&tags=跳槽,薪资&minLikes=10
```

## 📊 性能测试

### 响应时间要求
- 搜索响应时间 < 2秒
- 页面加载时间 < 3秒
- 高级筛选响应 < 1秒

### 并发测试
- 支持 100+ 并发搜索请求
- 数据库查询优化
- 缓存机制验证

## 🐛 已知问题

### 待修复问题
1. 搜索建议功能待实现
2. 搜索历史记录待添加
3. 搜索结果高亮待优化

### 优化建议
1. 添加搜索自动完成
2. 实现搜索结果缓存
3. 优化移动端体验
4. 添加搜索分析统计

## ✅ 测试检查清单

- [ ] 基础搜索功能正常
- [ ] 分类搜索工作正确
- [ ] 高级筛选功能完整
- [ ] 排序功能准确
- [ ] 响应式设计良好
- [ ] 加载状态显示正确
- [ ] 错误处理友好
- [ ] API 响应正常
- [ ] 性能满足要求
- [ ] 用户体验流畅

## 📝 测试报告模板

### 测试环境
- 浏览器：Chrome/Safari/Firefox
- 设备：Desktop/Mobile/Tablet
- 网络：WiFi/4G/3G

### 测试结果
- 通过用例数：__/15
- 失败用例数：__/15
- 阻塞问题数：__
- 性能问题数：__

### 问题汇总
| 问题ID | 问题描述 | 严重程度 | 状态 |
|--------|----------|----------|------|
| BUG001 | 描述     | 高/中/低 | 待修复 |

### 测试结论
- [ ] 功能测试通过
- [ ] 性能测试通过
- [ ] 用户体验良好
- [ ] 可以发布上线
