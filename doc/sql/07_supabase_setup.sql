-- Supabase 特定功能设置脚本
-- 版本: 1.0
-- 最后更新: 2025-07-14
-- 此文件包含为 WorkMates 项目在 Supabase 平台上启用特定功能所需的 SQL 命令。

-- ================================
-- 启用行级安全 (RLS)
-- ================================
-- 为所有需要保护的表启用行级安全。这是 Supabase 的一项核心安全功能，
-- 确保用户只能访问他们被授权查看的数据。

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.salaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.interviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- ================================
-- 实时功能
-- ================================
-- 将表添加到 Supabase 的实时发布中，这样客户端就可以订阅这些表的变更事件。

-- 为 'posts' 表的插入、更新和删除操作创建发布
CREATE PUBLICATION supabase_realtime FOR TABLE posts;

-- 为 'comments' 表的插入、更新和删除操作创建发布
CREATE PUBLICATION supabase_realtime_comments FOR TABLE comments;

-- ================================
-- 存储 (Storage) 设置
-- ================================
-- 为文件存储创建存储桶 (Buckets)。
-- 例如，一个用于存放公开访问的用户头像，另一个用于存放需要授权访问的私有文件。

-- 创建一个名为 'avatars' 的公共存储桶，用于存放用户头像。
-- 'public' 参数表示此存储桶中的文件可以通过 URL 公开访问。
INSERT INTO storage.buckets (id, name, public) VALUES ('avatars', 'avatars', true);

-- 创建一个名为 'private_documents' 的私有存储桶。
-- 文件将默认设置为私有，需要授权才能访问。
INSERT INTO storage.buckets (id, name, public) VALUES ('private_documents', 'private_documents', false);

-- ================================
-- 数据库函数 (用于 RPC)
-- ================================
-- 创建可以通过 Supabase API 作为远程过程调用 (RPC) 的数据库函数。

-- 示例函数: 获取用户的公开个人资料
-- 这是一个任何人都可以调用的安全函数，用于获取用户的非敏感信息。
CREATE OR REPLACE FUNCTION get_user_profile(user_id_param UUID)
RETURNS TABLE (username VARCHAR, profile_picture_url TEXT)
LANGUAGE plpgsql
SECURITY DEFINER -- 以函数定义者的权限执行
AS $$
BEGIN
  RETURN QUERY
  SELECT u.username, u.profile_picture_url
  FROM public.users AS u
  WHERE u.id = user_id_param;
END;
$$;

-- 授予 'anon' 和 'authenticated' 角色执行此函数的权限
GRANT EXECUTE ON FUNCTION get_user_profile(UUID) TO anon;
GRANT EXECUTE ON FUNCTION get_user_profile(UUID) TO authenticated;