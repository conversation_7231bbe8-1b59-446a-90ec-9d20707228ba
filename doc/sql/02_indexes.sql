-- WorkMates 数据库索引与约束
-- 版本: 1.1
-- 最后更新: 2025-07-14
-- 此文件包含 WorkMates 数据库的所有索引、外键和唯一约束。

-- ================================
-- 唯一约束 (通过唯一索引实现)
-- ================================

-- 强制用户的电子邮箱、用户名和电话号码唯一。
CREATE UNIQUE INDEX IF NOT EXISTS "users_email_key" ON "users"("email");
CREATE UNIQUE INDEX IF NOT EXISTS "users_username_key" ON "users"("username");
CREATE UNIQUE INDEX IF NOT EXISTS "users_phone_key" ON "users"("phone");

-- 确保一个用户在每个OAuth提供商处只能有一个账户。
CREATE UNIQUE INDEX IF NOT EXISTS "unique_provider_account" ON "accounts"("provider", "providerAccountId");

-- 确保会话令牌(sessionToken)是唯一的。
CREATE UNIQUE INDEX IF NOT EXISTS "sessions_sessionToken_key" ON "sessions"("sessionToken");

-- 确保验证令牌(token)是唯一的。
CREATE UNIQUE INDEX IF NOT EXISTS "verification_tokens_token_key" ON "verification_tokens"("token");
CREATE UNIQUE INDEX IF NOT EXISTS "unique_identifier_token" ON "verification_tokens"("identifier", "token");

-- 强制公司的名称是唯一的。
CREATE UNIQUE INDEX IF NOT EXISTS "companies_name_key" ON "companies"("name");
CREATE UNIQUE INDEX IF NOT EXISTS "companies_nameEn_key" ON "companies"("nameEn");

-- 防止用户重复点赞同一个评论或帖子。
CREATE UNIQUE INDEX IF NOT EXISTS "unique_user_comment_like" ON "likes"("userId", "commentId");
CREATE UNIQUE INDEX IF NOT EXISTS "unique_user_post_like" ON "likes"("userId", "postId");

-- 防止用户重复收藏同一个帖子。
CREATE UNIQUE INDEX IF NOT EXISTS "unique_user_post_bookmark" ON "bookmarks"("userId", "postId");

-- 确保一个用户只能对一家公司评分一次。
CREATE UNIQUE INDEX IF NOT EXISTS "unique_user_company_rating" ON "ratings"("authorId", "companyId");

-- 防止用户重复上传同一个文件。
CREATE UNIQUE INDEX IF NOT EXISTS "unique_file_hash_user" ON "files"("fileHash", "uploadedById");

-- 确保每个用户只有一条信誉记录。
CREATE UNIQUE INDEX IF NOT EXISTS "user_credibility_userId_key" ON "user_credibility"("userId");

-- 确保每个用户只有一条通知设置记录。
CREATE UNIQUE INDEX IF NOT EXISTS "notification_settings_userId_key" ON "notification_settings"("userId");

-- ================================
-- 外键约束
-- ================================

-- 将 accounts 表链接到 users 表。
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 sessions 表链接到 users 表。
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 posts 表链接到其作者 (users 表)。
ALTER TABLE "posts" ADD CONSTRAINT "posts_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 posts 表链接到 a company。
ALTER TABLE "posts" ADD CONSTRAINT "posts_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;
-- 将 comments 表链接到其作者 (users 表)。
ALTER TABLE "comments" ADD CONSTRAINT "comments_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 likes 表链接到点赞的用户。
ALTER TABLE "likes" ADD CONSTRAINT "likes_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 bookmarks 表链接到收藏的用户。
ALTER TABLE "bookmarks" ADD CONSTRAINT "bookmarks_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- 将 salaries 表链接到提交薪资的用户。
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- 将 salaries 表链接到 a company。
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- 将 interviews 表链接到提交面试经验的用户。
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- 将 interviews 表链接到 a company。
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- 将 ratings 表链接到提交评分的用户。
ALTER TABLE "ratings" ADD CONSTRAINT "ratings_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- 将 ratings 表链接到 a company。
ALTER TABLE "ratings" ADD CONSTRAINT "ratings_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
-- 将 reports 表链接到被举报的用户。
ALTER TABLE "reports" ADD CONSTRAINT "reports_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
-- 将 work_experiences 表链接到 a user。
ALTER TABLE "work_experiences" ADD CONSTRAINT "work_experiences_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 experience_files 表链接到 a work experience。
ALTER TABLE "experience_files" ADD CONSTRAINT "experience_files_workExperienceId_fkey" FOREIGN KEY ("workExperienceId") REFERENCES "work_experiences"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 file_uploads 表链接到上传文件的用户。
ALTER TABLE "file_uploads" ADD CONSTRAINT "file_uploads_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 notifications 表链接到接收通知的用户。
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- 将 notifications 表链接到发送通知的用户。
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
-- 将 notification_settings 表链接到 a user。
ALTER TABLE "notification_settings" ADD CONSTRAINT "notification_settings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ================================
-- 性能优化索引
-- ================================

-- 优化按用户、状态和创建日期查询通知的性能。
CREATE INDEX IF NOT EXISTS "notifications_userId_status_createdAt_idx" ON "notifications"("userId", "status", "createdAt");
-- 优化按用户和类型查询通知的性能。
CREATE INDEX IF NOT EXISTS "notifications_userId_type_idx" ON "notifications"("userId", "type");
-- 优化按关联实体查询通知的性能。
CREATE INDEX IF NOT EXISTS "notifications_relatedId_relatedType_idx" ON "notifications"("relatedId", "relatedType");
-- 优化按创建日期排序通知的性能。
CREATE INDEX IF NOT EXISTS "notifications_createdAt_idx" ON "notifications"("createdAt");
