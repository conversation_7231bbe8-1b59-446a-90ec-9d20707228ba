/**
 * Cloudflare R2 连接测试脚本
 * 验证 R2 配置是否正确，能否正常连接和操作
 */

const { S3Client, ListObjectsV2Command, PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3')
require('dotenv').config({ path: '.env.local' })

// 验证环境变量
function validateEnvironment() {
  const requiredVars = [
    'R2_ACCOUNT_ID',
    'R2_ACCESS_KEY_ID', 
    'R2_SECRET_ACCESS_KEY',
    'R2_BUCKET_NAME'
  ]

  const missing = requiredVars.filter(varName => !process.env[varName])
  
  if (missing.length > 0) {
    console.error('❌ 缺少必要的环境变量:')
    missing.forEach(varName => {
      console.error(`   - ${varName}`)
    })
    return false
  }

  console.log('✅ 环境变量检查通过')
  console.log(`   - 账户ID: ${process.env.R2_ACCOUNT_ID}`)
  console.log(`   - 存储桶: ${process.env.R2_BUCKET_NAME}`)
  console.log(`   - 访问密钥: ${process.env.R2_ACCESS_KEY_ID}`)
  console.log(`   - 公共URL: ${process.env.R2_PUBLIC_URL || '未设置'}`)
  return true
}

// 创建 R2 客户端
function createR2Client() {
  try {
    const client = new S3Client({
      region: 'auto',
      endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
      },
    })
    console.log('✅ R2 客户端创建成功')
    return client
  } catch (error) {
    console.error('❌ R2 客户端创建失败:', error.message)
    return null
  }
}

// 测试存储桶访问
async function testBucketAccess(client) {
  try {
    console.log('\n🔍 测试存储桶访问...')
    
    const command = new ListObjectsV2Command({
      Bucket: process.env.R2_BUCKET_NAME,
      MaxKeys: 5
    })

    const response = await client.send(command)
    console.log('✅ 存储桶访问成功')
    console.log(`   - 对象数量: ${response.KeyCount || 0}`)
    
    if (response.Contents && response.Contents.length > 0) {
      console.log('   - 最近的文件:')
      response.Contents.slice(0, 3).forEach(obj => {
        console.log(`     * ${obj.Key} (${obj.Size} bytes)`)
      })
    }
    
    return true
  } catch (error) {
    console.error('❌ 存储桶访问失败:', error.message)
    return false
  }
}

// 测试文件上传
async function testFileUpload(client) {
  try {
    console.log('\n📤 测试文件上传...')
    
    const testContent = `R2 连接测试文件
创建时间: ${new Date().toISOString()}
测试内容: Hello Cloudflare R2!`
    
    const testKey = `test/connection-test-${Date.now()}.txt`
    
    const command = new PutObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: testKey,
      Body: Buffer.from(testContent, 'utf-8'),
      ContentType: 'text/plain',
      Metadata: {
        testFile: 'true',
        createdBy: 'r2-connection-test',
        timestamp: new Date().toISOString()
      }
    })

    await client.send(command)
    console.log('✅ 文件上传成功')
    console.log(`   - 文件键: ${testKey}`)
    console.log(`   - 文件大小: ${Buffer.byteLength(testContent, 'utf-8')} bytes`)
    
    // 生成访问URL
    const publicUrl = process.env.R2_CUSTOM_DOMAIN 
      ? `${process.env.R2_CUSTOM_DOMAIN}/${testKey}`
      : `${process.env.R2_PUBLIC_URL}/${testKey}`
    
    console.log(`   - 访问URL: ${publicUrl}`)
    
    return testKey
  } catch (error) {
    console.error('❌ 文件上传失败:', error.message)
    return null
  }
}

// 测试文件删除
async function testFileDelete(client, key) {
  try {
    console.log('\n🗑️  测试文件删除...')
    
    const command = new DeleteObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: key
    })

    await client.send(command)
    console.log('✅ 文件删除成功')
    console.log(`   - 已删除: ${key}`)
    
    return true
  } catch (error) {
    console.error('❌ 文件删除失败:', error.message)
    return false
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始 Cloudflare R2 连接测试\n')
  
  // 1. 验证环境变量
  if (!validateEnvironment()) {
    process.exit(1)
  }

  // 2. 创建客户端
  const client = createR2Client()
  if (!client) {
    process.exit(1)
  }

  // 3. 测试存储桶访问
  const bucketAccessOk = await testBucketAccess(client)
  if (!bucketAccessOk) {
    console.log('\n❌ 基础连接测试失败，请检查配置')
    process.exit(1)
  }

  // 4. 测试文件上传
  const uploadedKey = await testFileUpload(client)
  if (!uploadedKey) {
    console.log('\n❌ 文件上传测试失败')
    process.exit(1)
  }

  // 5. 测试文件删除
  const deleteOk = await testFileDelete(client, uploadedKey)
  if (!deleteOk) {
    console.log('\n⚠️  文件删除测试失败，但上传功能正常')
  }

  console.log('\n🎉 所有测试完成！')
  console.log('✅ Cloudflare R2 集成配置正确，可以正常使用')
  
  console.log('\n📋 下一步操作:')
  console.log('   1. 访问 http://localhost:3000/test/r2-upload 测试头像上传')
  console.log('   2. 在用户资料页面测试头像更换功能')
  console.log('   3. 检查上传的文件是否可以正常访问')
}

// 运行测试
runTests().catch(error => {
  console.error('\n💥 测试过程中发生错误:', error)
  process.exit(1)
})
